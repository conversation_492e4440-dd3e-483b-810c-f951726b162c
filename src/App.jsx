import { useState } from 'react'
import './App.css'

// Import components
import Home from './components/Home'
import Exercise1 from './components/Exercise1'
import Exercise2 from './components/Exercise2'
import Exercise3 from './components/Exercise3'
import Navigation from './components/Navigation'

// Import new multi-module components
import StoryPointModule from './components/modules/story-points/StoryPointModule'
import Breadcrumbs from './components/common/Breadcrumbs'

// Import utilities
import { NavigationManager, createInitialNavigationState, generateBreadcrumbs, migrateLegacyNavigation } from './utils/routingUtils'
import { ProgressManager, migrateLegacyProgress } from './utils/progressManager'

function App() {
  // Legacy state (maintained for backward compatibility)
  const [currentView, setCurrentView] = useState('home') // 'home' or exercise number
  const [exerciseProgress, setExerciseProgress] = useState({
    1: { completed: false, started: false },
    2: { completed: false, started: false },
    3: { completed: false, started: false }
  })

  // New multi-module state
  const [navigationState, setNavigationState] = useState(createInitialNavigationState())
  const [moduleProgress, setModuleProgress] = useState({})
  const [useNewNavigation, setUseNewNavigation] = useState(false) // Feature flag for gradual migration

  // Initialize managers
  const [navigationManager] = useState(() => new NavigationManager(navigationState))
  const [progressManager] = useState(() => new ProgressManager(moduleProgress))

  const handleExerciseComplete = (exerciseNumber) => {
    setExerciseProgress(prev => ({
      ...prev,
      [exerciseNumber]: { ...prev[exerciseNumber], completed: true }
    }))

    // Auto-advance to next exercise if not the last one
    if (exerciseNumber < 3) {
      setCurrentView(exerciseNumber + 1)
      setExerciseProgress(prev => ({
        ...prev,
        [exerciseNumber + 1]: { ...prev[exerciseNumber + 1], started: true }
      }))
    } else {
      // After completing the last exercise, return to home
      setCurrentView('home')
    }
  }

  const handleExerciseStart = (exerciseNumber) => {
    setExerciseProgress(prev => ({
      ...prev,
      [exerciseNumber]: { ...prev[exerciseNumber], started: true }
    }))
  }

  const navigateToExercise = (exerciseNumber) => {
    setCurrentView(exerciseNumber)
  }

  const navigateToHome = () => {
    setCurrentView('home')
  }

  const startFresh = () => {
    setExerciseProgress({
      1: { completed: false, started: false },
      2: { completed: false, started: false },
      3: { completed: false, started: false }
    })
    setCurrentView('home')

    // Also reset new navigation state
    const newNavState = navigationManager.navigateToHome()
    setNavigationState(newNavState)
    progressManager.resetAll()
    setModuleProgress({})
  }

  // New navigation handlers
  const handleNewNavigation = (type, moduleId, exerciseId) => {
    let newNavState

    switch (type) {
      case 'home':
        newNavState = navigationManager.navigateToHome()
        break
      case 'module':
        newNavState = navigationManager.navigateToModule(moduleId)
        break
      case 'exercise':
        newNavState = navigationManager.navigateToExercise(moduleId, exerciseId)
        break
      default:
        return
    }

    setNavigationState(newNavState)
  }

  const handleModuleExerciseStart = (moduleId, exerciseId) => {
    const newProgress = progressManager.startExercise(moduleId, exerciseId)
    setModuleProgress({ ...newProgress })
  }

  const handleModuleExerciseComplete = (moduleId, exerciseId) => {
    // For now, we'll use a simple completion without module config
    // This will be enhanced when we fully integrate the module system
    const newProgress = progressManager.completeExercise(moduleId, exerciseId, [
      { id: 1 }, { id: 2 }, { id: 3 }
    ])
    setModuleProgress({ ...newProgress })
  }

  const renderCurrentView = () => {
    // Use new navigation system if enabled
    if (useNewNavigation) {
      return renderNewNavigationView()
    }

    // Legacy navigation system (default for now)
    if (currentView === 'home') {
      return (
        <Home
          exerciseProgress={exerciseProgress}
          onNavigate={navigateToExercise}
          onStartFresh={startFresh}
        />
      )
    }

    // Render exercise based on currentView number
    switch (currentView) {
      case 1:
        return (
          <Exercise1
            onComplete={() => handleExerciseComplete(1)}
            onStart={() => handleExerciseStart(1)}
            isStarted={exerciseProgress[1].started}
          />
        )
      case 2:
        return (
          <Exercise2
            onComplete={() => handleExerciseComplete(2)}
            onStart={() => handleExerciseStart(2)}
            isStarted={exerciseProgress[2].started}
          />
        )
      case 3:
        return (
          <Exercise3
            onComplete={() => handleExerciseComplete(3)}
            onStart={() => handleExerciseStart(3)}
            isStarted={exerciseProgress[3].started}
          />
        )
      default:
        return null
    }
  }

  const renderNewNavigationView = () => {
    if (navigationState.currentView === 'home') {
      return (
        <Home
          exerciseProgress={exerciseProgress}
          onNavigate={navigateToExercise}
          onStartFresh={startFresh}
        />
      )
    }

    if (navigationState.currentView === 'module' && navigationState.currentModule === 'story-points') {
      return (
        <StoryPointModule
          currentExercise={navigationState.currentExercise}
          moduleProgress={moduleProgress['story-points'] || { exercises: {} }}
          onExerciseComplete={handleModuleExerciseComplete}
          onExerciseStart={handleModuleExerciseStart}
          onNavigate={handleNewNavigation}
        />
      )
    }

    return null
  }

  // Generate breadcrumbs for new navigation
  const breadcrumbs = useNewNavigation ? generateBreadcrumbs(navigationState) : []

  return (
    <div className="app">
      {useNewNavigation && breadcrumbs.length > 1 && (
        <Breadcrumbs
          breadcrumbs={breadcrumbs}
          onNavigate={handleNewNavigation}
        />
      )}
      <main className="main-content">
        {renderCurrentView()}
      </main>
      {!useNewNavigation && currentView !== 'home' && (
        <Navigation
          currentExercise={currentView}
          exerciseProgress={exerciseProgress}
          onNavigate={navigateToExercise}
          onNavigateHome={navigateToHome}
        />
      )}

      {/* Temporary toggle for testing new navigation */}
      <div style={{ position: 'fixed', bottom: '20px', right: '20px', zIndex: 1000 }}>
        <button
          onClick={() => setUseNewNavigation(!useNewNavigation)}
          style={{
            padding: '8px 16px',
            background: useNewNavigation ? '#28a745' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          {useNewNavigation ? 'Use Legacy Nav' : 'Use New Nav'}
        </button>
      </div>
    </div>
  )
}

export default App
