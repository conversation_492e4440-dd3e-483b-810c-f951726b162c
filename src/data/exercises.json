{"metadata": {"version": "1.0.0", "lastUpdated": "2025-01-07", "description": "Exercise metadata and configuration for Story Point Master"}, "exercises": [{"id": 1, "title": "Abstract Comparisons", "description": "Learn relative sizing with abstract items", "details": "Start with simple abstract concepts to understand relative effort without real-world bias.", "type": "sorting_and_pointing", "config": {"allowShuffle": true, "showMindsetReminder": true, "pointScale": [1, 2, 3, 5, 8, 13]}, "ui": {"startScreen": {"instructions": "You'll be presented with abstract items like \"a grain of sand,\" \"a pebble,\" \"a boulder,\" and \"a mountain.\" Your task is to arrange them by relative effort to move, then assign story points.", "buttonText": "Start Exercise"}, "mindsetReminder": "Remember: Story points are about relative effort, not absolute time. A '3' is roughly three times the effort of a '1'."}}, {"id": 2, "title": "User Stories", "description": "Apply sizing to real user stories", "details": "Practice with realistic user stories from a typical web application development scenario.", "type": "sorting_and_pointing", "config": {"allowShuffle": true, "showMindsetReminder": false, "pointScale": [1, 2, 3, 5, 8, 13]}, "ui": {"startScreen": {"instructions": "You'll work with realistic user stories from a typical web application. First arrange them by relative complexity, then assign story points (1, 2, 3, 5, 8, 13) based on complexity, effort, and uncertainty.", "buttonText": "Start Exercise"}}}, {"id": 3, "title": "Core Principles", "description": "Review and reinforce key concepts", "details": "Test your knowledge with an interactive quiz covering fundamental story point principles.", "type": "quiz", "config": {"showProgress": true, "allowReview": true}, "ui": {"startScreen": {"instructions": "Complete an interactive quiz covering the fundamental principles of story point estimation, then review a comprehensive summary of everything you've learned.", "buttonText": "Start Final Quiz", "preview": {"title": "What You'll Cover:", "items": ["✓ Relative sizing vs. time estimation", "✓ The three factors: complexity, effort, uncertainty", "✓ <PERSON><PERSON><PERSON><PERSON> sequence and scaling", "✓ Team velocity and planning", "✓ Common mistakes to avoid"]}}}}]}