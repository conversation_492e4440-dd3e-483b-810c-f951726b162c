/* Exercise 3: Core Principles Recap Styles */

.quiz-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.quiz-preview h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.quiz-preview ul {
  margin: 0;
  padding-left: 20px;
}

.quiz-preview li {
  margin: 8px 0;
  color: #34495e;
}

/* Quiz Container */
.quiz-container {
  max-width: 700px;
  margin: 0 auto;
}

.quiz-progress {
  margin-bottom: 30px;
  text-align: center;
}

.quiz-progress span {
  display: block;
  margin-bottom: 10px;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
}

/* Question Card */
.question-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.question-category {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 20px;
}

.question-text {
  font-size: 1.3rem;
  line-height: 1.5;
  margin: 0 0 30px 0;
  color: #2c3e50;
}

.question-instruction {
  margin-bottom: 20px;
}

.instruction-text {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  font-weight: 500;
  text-align: center;
}

/* Answer Buttons */
.answer-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.answer-button {
  flex: 1;
  max-width: 200px;
  padding: 15px 30px;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.true-button {
  background: #27ae60;
  color: white;
}

.true-button:hover {
  background: #229954;
  transform: translateY(-2px);
}

.false-button {
  background: #e74c3c;
  color: white;
}

.false-button:hover {
  background: #c0392b;
  transform: translateY(-2px);
}

/* Question Feedback */
.question-feedback {
  text-align: center;
}

.feedback-result {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.feedback-result.correct {
  background: #d5f4e6;
  color: #27ae60;
  border: 2px solid #27ae60;
}

.feedback-result.incorrect {
  background: #fadbd8;
  color: #e74c3c;
  border: 2px solid #e74c3c;
}

.result-icon {
  font-size: 1.5rem;
}

.feedback-explanation {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  line-height: 1.6;
  color: #2c3e50;
  text-align: left;
}

.next-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.next-button:hover {
  background: #2980b9;
}

/* Results Container */
.results-container {
  max-width: 800px;
  margin: 0 auto;
}

.results-header {
  text-align: center;
  margin-bottom: 40px;
}

.results-header h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 2rem;
}

.score-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.score-number {
  font-size: 3rem;
  font-weight: bold;
  color: #27ae60;
}

.score-details {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Results Breakdown */
.results-breakdown h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.result-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  border-left: 4px solid #bdc3c7;
}

.result-item.correct {
  border-left-color: #27ae60;
  background: #f8fff9;
}

.result-item.incorrect {
  border-left-color: #e74c3c;
  background: #fffafa;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.question-number {
  background: #34495e;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.result-icon.correct {
  color: #27ae60;
  font-weight: bold;
}

.result-icon.incorrect {
  color: #e74c3c;
  font-weight: bold;
}

.result-category {
  background: #ecf0f1;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.result-question {
  margin: 10px 0;
  font-weight: 500;
  color: #2c3e50;
}

.result-answers {
  display: flex;
  gap: 20px;
  margin: 10px 0;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.result-explanation {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;
  font-style: italic;
  color: #5d6d7e;
  line-height: 1.5;
}

.results-actions {
  text-align: center;
  margin-top: 40px;
}

.summary-button {
  background: #9b59b6;
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.summary-button:hover {
  background: #8e44ad;
}

/* Summary Container */
.summary-container {
  max-width: 900px;
  margin: 0 auto;
}

.summary-header {
  text-align: center;
  margin-bottom: 40px;
}

.summary-header h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 2.2rem;
}

.summary-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

/* Key Learnings */
.key-learnings {
  margin-bottom: 40px;
}

.key-learnings h4 {
  margin: 0 0 30px 0;
  color: #2c3e50;
  font-size: 1.5rem;
  text-align: center;
}

.learning-section {
  background: white;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
}

.learning-section h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.learning-section ul {
  margin: 0;
  padding-left: 20px;
}

.learning-section li {
  margin: 8px 0;
  color: #34495e;
  line-height: 1.5;
}

/* Practical Tips */
.practical-tips {
  margin-bottom: 40px;
}

.practical-tips h4 {
  margin: 0 0 30px 0;
  color: #2c3e50;
  font-size: 1.5rem;
  text-align: center;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.tip-card {
  background: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #e67e22;
}

.tip-card h6 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.tip-card ul {
  margin: 0;
  padding-left: 20px;
}

.tip-card li {
  margin: 8px 0;
  color: #34495e;
  line-height: 1.4;
  font-size: 0.95rem;
}

/* Completion Message */
.completion-message {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-bottom: 30px;
}

.completion-message h4 {
  margin: 0 0 20px 0;
  font-size: 1.5rem;
}

.completion-message p {
  margin: 15px 0;
  line-height: 1.6;
  font-size: 1.05rem;
}

.completion-message strong {
  font-weight: 600;
}

.summary-actions {
  text-align: center;
}

.complete-button {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 18px 50px;
  border-radius: 10px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.complete-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .question-card {
    padding: 20px;
  }

  .question-text {
    font-size: 1.1rem;
  }

  .answer-buttons {
    flex-direction: column;
  }

  .answer-button {
    max-width: none;
  }

  .result-answers {
    flex-direction: column;
    gap: 5px;
  }

  .result-header {
    flex-wrap: wrap;
  }

  .score-number {
    font-size: 2.5rem;
  }

  .tips-grid {
    grid-template-columns: 1fr;
  }

  .learning-section,
  .tip-card {
    padding: 20px;
  }

  .completion-message {
    padding: 25px;
  }

  .summary-header h3 {
    font-size: 1.8rem;
  }
}
