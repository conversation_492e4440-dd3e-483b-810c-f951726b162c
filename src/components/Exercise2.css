/* Exercise 2 Specific Styles */

.ordering-step, .pointing-step, .feedback-step {
  width: 100%;
  max-width: 700px;
}

.ordering-step h3, .pointing-step h3, .feedback-step h3 {
  font-size: 1.5rem;
  color: #212529;
  margin-bottom: 1rem;
  text-align: center;
}

/* Step Question Styles */
.step-question {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2px solid #f39c12;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 3px 10px rgba(243, 156, 18, 0.2);
}

.question-prompt {
  font-size: 1.1rem;
  color: #d68910;
  margin: 0 0 0.75rem 0;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.question-text {
  font-size: 1rem;
  color: #856404;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
}

.ordering-step p, .pointing-step p {
  text-align: center;
  color: #6c757d;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.sortable-list {
  margin-bottom: 2rem;
}

.next-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.next-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.next-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* Pointing Step Styles */
.pointing-list {
  margin-bottom: 2rem;
}

.pointing-story {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1.5rem;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
  gap: 1rem;
}

.story-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.story-position {
  font-weight: 700;
  font-size: 1.2rem;
  color: #667eea;
  min-width: 2.5rem;
  margin-top: 0.25rem;
}

.story-details strong {
  display: block;
  color: #212529;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.story-description {
  font-size: 0.95rem;
  color: #495057;
  margin-bottom: 1rem;
  line-height: 1.4;
  font-style: italic;
}

.acceptance-criteria {
  font-size: 0.9rem;
}

.acceptance-criteria strong {
  color: #495057;
  margin-bottom: 0.5rem;
}

.acceptance-criteria ul {
  margin: 0.5rem 0 0 0;
  padding-left: 1.2rem;
  color: #6c757d;
}

.acceptance-criteria li {
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

/* Point selector styles are now in PointSelector.css */

/* Feedback Step Styles */
.feedback-section {
  margin-bottom: 2rem;
}

.feedback-section h4, .key-learning h4 {
  font-size: 1.2rem;
  color: #212529;
  margin-bottom: 1rem;
}

.correct-answers {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.feedback-story {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 6px;
  margin-bottom: 1rem;
  border-left: 4px solid #28a745;
}

.feedback-story:last-child {
  margin-bottom: 0;
}

.story-rank {
  font-weight: 700;
  font-size: 1.2rem;
  color: #28a745;
  min-width: 2.5rem;
  margin-top: 0.25rem;
}

.story-content {
  flex: 1;
}

.story-header {
  margin-bottom: 0.75rem;
}

.story-header strong {
  color: #212529;
  font-size: 1.1rem;
}

.points {
  color: #667eea;
  font-weight: 600;
}

.complexity-breakdown {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.complexity-item {
  font-size: 0.85rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.complexity-item strong {
  color: #495057;
}

.explanation {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0;
}

.key-learning {
  background: linear-gradient(135deg, #f8f9ff 0%, #fff5f5 100%);
  border-left: 4px solid #667eea;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.key-learning ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #495057;
}

.key-learning li {
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.key-learning li:last-child {
  margin-bottom: 0;
}

.key-learning strong {
  color: #212529;
}

@media (max-width: 768px) {
  .step-question {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .question-prompt {
    font-size: 1rem;
  }

  .question-text {
    font-size: 0.9rem;
  }

  .pointing-story {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .point-selector {
    align-self: flex-end;
    flex-direction: row;
    align-items: center;
  }
  
  .feedback-story {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .story-rank {
    align-self: flex-start;
  }
  
  .complexity-breakdown {
    flex-direction: column;
    gap: 0.5rem;
  }
}
