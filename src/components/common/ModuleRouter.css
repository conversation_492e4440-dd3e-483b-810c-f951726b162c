.module-router {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* Module Overview Styles */
.module-overview {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.module-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.module-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.module-description {
  font-size: 1.25rem;
  color: #666;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.learning-objectives {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border-left: 4px solid #007bff;
}

.learning-objectives h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.learning-objectives ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.learning-objectives li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 24px;
}

.learning-objectives li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

/* Exercise List Styles */
.exercise-list h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 24px 0;
  text-align: center;
}

.exercise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.exercise-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
}

.exercise-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.exercise-card.not-started {
  border-color: #e9ecef;
}

.exercise-card.in-progress {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fff, #fffbf0);
}

.exercise-card.completed {
  border-color: #28a745;
  background: linear-gradient(135deg, #fff, #f8fff9);
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.exercise-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.exercise-status {
  flex-shrink: 0;
}

.status-completed {
  color: #28a745;
  font-weight: 500;
  font-size: 0.875rem;
}

.status-in-progress {
  color: #ffc107;
  font-weight: 500;
  font-size: 0.875rem;
}

.status-not-started {
  color: #6c757d;
  font-weight: 500;
  font-size: 0.875rem;
}

.exercise-description {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 8px 0;
}

.exercise-details {
  color: #888;
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0 0 20px 0;
}

.exercise-button {
  width: 100%;
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exercise-button:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.exercise-button:active {
  transform: translateY(0);
}

.exercise-card.completed .exercise-button {
  background: #28a745;
}

.exercise-card.completed .exercise-button:hover {
  background: #1e7e34;
}

/* Exercise Placeholder Styles */
.exercise-placeholder {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.exercise-placeholder-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
}

.exercise-placeholder-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.exercise-placeholder-status {
  color: #666;
  font-size: 1rem;
}

.exercise-placeholder-content {
  margin-bottom: 32px;
}

.exercise-placeholder-content p {
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.placeholder-note {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
}

.placeholder-note p {
  margin: 0 0 8px 0;
  color: #1565c0;
}

.placeholder-note p:last-child {
  margin-bottom: 0;
}

.exercise-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.start-button {
  background: #28a745;
  color: white;
}

.start-button:hover {
  background: #1e7e34;
}

.complete-button {
  background: #007bff;
  color: white;
}

.complete-button:hover {
  background: #0056b3;
}

.back-button {
  background: #6c757d;
  color: white;
}

.back-button:hover {
  background: #545b62;
}

/* Loading and Error States */
.module-router-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.module-router-error {
  text-align: center;
  padding: 64px 24px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 12px;
  color: #721c24;
}

.module-router-error h2 {
  margin: 0 0 16px 0;
}

.module-router-error button {
  margin-top: 16px;
  padding: 12px 24px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.module-router-error button:hover {
  background: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .module-router {
    padding: 16px;
  }
  
  .module-title {
    font-size: 2rem;
  }
  
  .module-description {
    font-size: 1.125rem;
  }
  
  .exercise-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .exercise-card {
    padding: 20px;
  }
  
  .exercise-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .exercise-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
}
